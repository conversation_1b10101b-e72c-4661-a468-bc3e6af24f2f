{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "CommonJS", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "./build", "rootDir": ".", "declaration": true, "sourceMap": true}, "include": ["**/*.ts"], "exclude": ["node_modules", "build"]}