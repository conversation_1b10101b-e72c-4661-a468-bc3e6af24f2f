# UnisonX Assets

This directory contains branding and build assets for UnisonX.

## Icons
- `icon.ico` - Main application icon (Windows ICO format)
- `icon.png` - High-resolution PNG version 
- `tray-icon.png` - System tray icon
- `file-icon.ico` - File association icon for .unisonx files

## Installer Assets
- `installer.nsh` - NSIS installer script with advanced configuration
- `license.txt` - MIT license and application information
- `header.bmp` - Installer header image (150x57 pixels)
- `welcome.bmp` - Welcome page sidebar image (164x314 pixels)

## Build Resources
These assets are automatically included in the built application and installer.

## Icon Requirements
- icon.ico: 256x256, 128x128, 64x64, 48x48, 32x32, 16x16 (multi-size ICO)
- icon.png: 512x512 or 1024x1024 (high-DPI displays)
- tray-icon.png: 16x16, 32x32 (system tray)

## Creating Icons
For production, create professional icons using tools like:
- Adobe Illustrator
- Figma
- Sketch
- Canva Pro
- IconMaker or similar icon generation tools

Current icons are placeholders and should be replaced with professional branding.