{"name": "unisonx", "version": "1.0.0", "description": "Ultra-advanced iPhone-Windows integration application", "main": "src/main/build/main.js", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:renderer\" \"wait-on http://localhost:5173 && electron .\"", "dev:renderer": "vite", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p src/main/tsconfig.json", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:portable": "npm run build && electron-builder --win portable", "pack": "npm run build && electron-builder --dir", "prepack": "npm run build", "predist": "npm run build", "test": "jest", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "typecheck": "tsc --noEmit"}, "keywords": ["iPhone", "Windows", "sync", "integration", "electron"], "author": "UnisonX Development Team", "license": "MIT", "devDependencies": {"@types/better-sqlite3": "^7.6.8", "@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^28.1.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "typescript": "^5.3.3", "vite": "^7.0.6", "wait-on": "^7.2.0"}, "dependencies": {"autoprefixer": "^10.4.16", "axios": "^1.6.2", "better-sqlite3": "^9.2.2", "clsx": "^2.0.0", "dayjs": "^1.11.10", "electron-log": "^5.0.1", "electron-updater": "^6.1.7", "lucide-react": "^0.298.0", "node-hid": "^2.1.2", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "tailwindcss": "^3.3.6", "usb": "^2.11.0"}, "build": {"appId": "com.unisonx.app", "productName": "UnisonX", "copyright": "Copyright © 2024 UnisonX Development Team", "description": "Ultra-advanced iPhone-Windows integration application - 1000x better than Intel Unison", "directories": {"output": "dist", "buildResources": "assets"}, "files": ["build/**/*", "src/main/build/**/*", "node_modules/**/*", "!node_modules/**/*.{md,txt,map}", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "requireAdministrator", "artifactName": "${productName}-${version}-${arch}.${ext}", "publisherName": "UnisonX Development Team", "verifyUpdateCodeSignature": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "UnisonX", "include": "assets/installer.nsh", "script": "assets/installer.nsh"}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}, "fileAssociations": [{"ext": "unisonx", "name": "UnisonX Backup File", "description": "UnisonX backup and configuration file", "icon": "assets/file-icon.ico"}], "protocols": [{"name": "UnisonX Protocol", "schemes": ["unisonx"]}]}}